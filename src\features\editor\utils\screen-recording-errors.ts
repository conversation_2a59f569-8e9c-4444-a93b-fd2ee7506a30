export interface ScreenRecordingError {
  code: string;
  message: string;
  userMessage: string;
  suggestion?: string;
}

export const SCREEN_RECORDING_ERRORS = {
  NOT_SUPPORTED: {
    code: 'NOT_SUPPORTED',
    message: 'Screen recording is not supported in this browser',
    userMessage: 'Screen recording is not supported',
    suggestion: 'Please use a modern browser like Chrome, Firefox, or Edge'
  },
  PERMISSION_DENIED: {
    code: 'PERMISSION_DENIED',
    message: 'Screen recording permission was denied',
    userMessage: 'Permission denied',
    suggestion: 'Please allow screen recording permission and try again'
  },
  NO_MEDIA_FOUND: {
    code: 'NO_MEDIA_FOUND',
    message: 'No screen capture source available',
    userMessage: 'No screen available',
    suggestion: 'Make sure you have a screen or window available to record'
  },
  RECORDING_FAILED: {
    code: 'RECORDING_FAILED',
    message: 'Recording failed due to technical error',
    userMessage: 'Recording failed',
    suggestion: 'Please try again or restart your browser'
  },
  PROCESSING_FAILED: {
    code: 'PROCESSING_FAILED',
    message: 'Failed to process recorded video',
    userMessage: 'Processing failed',
    suggestion: 'The recording may be corrupted. Please try recording again'
  },
  UPLOAD_FAILED: {
    code: 'UPLOAD_FAILED',
    message: 'Failed to add recording to timeline',
    userMessage: 'Upload failed',
    suggestion: 'Please try adding the recording to timeline manually'
  },
  BROWSER_COMPATIBILITY: {
    code: 'BROWSER_COMPATIBILITY',
    message: 'Browser does not support required features',
    userMessage: 'Browser not compatible',
    suggestion: 'Please update your browser or use Chrome, Firefox, or Edge'
  },
  CODEC_NOT_SUPPORTED: {
    code: 'CODEC_NOT_SUPPORTED',
    message: 'Video codec not supported',
    userMessage: 'Video format not supported',
    suggestion: 'Your browser may not support the video format. Try a different browser'
  }
} as const;

export function getScreenRecordingError(error: unknown): ScreenRecordingError {
  if (error instanceof Error) {
    switch (error.name) {
      case 'NotAllowedError':
        return SCREEN_RECORDING_ERRORS.PERMISSION_DENIED;
      case 'NotSupportedError':
        return SCREEN_RECORDING_ERRORS.NOT_SUPPORTED;
      case 'NotFoundError':
        return SCREEN_RECORDING_ERRORS.NO_MEDIA_FOUND;
      case 'AbortError':
        return {
          code: 'ABORTED',
          message: 'Recording was aborted',
          userMessage: 'Recording cancelled',
          suggestion: 'Recording was stopped by user or system'
        };
      case 'SecurityError':
        return {
          code: 'SECURITY_ERROR',
          message: 'Security error during recording',
          userMessage: 'Security error',
          suggestion: 'Please check your browser security settings'
        };
      default:
        if (error.message.includes('codec')) {
          return SCREEN_RECORDING_ERRORS.CODEC_NOT_SUPPORTED;
        }
        if (error.message.includes('permission')) {
          return SCREEN_RECORDING_ERRORS.PERMISSION_DENIED;
        }
        if (error.message.includes('not supported')) {
          return SCREEN_RECORDING_ERRORS.NOT_SUPPORTED;
        }
        return {
          code: 'UNKNOWN_ERROR',
          message: error.message,
          userMessage: 'An unexpected error occurred',
          suggestion: 'Please try again or contact support if the problem persists'
        };
    }
  }

  return {
    code: 'UNKNOWN_ERROR',
    message: 'Unknown error occurred',
    userMessage: 'An unexpected error occurred',
    suggestion: 'Please try again or contact support if the problem persists'
  };
}

export function checkBrowserCompatibility(): { 
  isSupported: boolean; 
  missingFeatures: string[]; 
  error?: ScreenRecordingError 
} {
  const missingFeatures: string[] = [];

  // Check for basic browser APIs
  if (typeof navigator === 'undefined') {
    missingFeatures.push('Navigator API');
  }

  if (!navigator.mediaDevices) {
    missingFeatures.push('MediaDevices API');
  }

  if (!navigator.mediaDevices?.getDisplayMedia) {
    missingFeatures.push('Screen Capture API');
  }

  if (typeof MediaRecorder === 'undefined') {
    missingFeatures.push('MediaRecorder API');
  }

  // Check for Blob support
  if (typeof Blob === 'undefined') {
    missingFeatures.push('Blob API');
  }

  // Check for URL.createObjectURL support
  if (!URL?.createObjectURL) {
    missingFeatures.push('Object URL API');
  }

  const isSupported = missingFeatures.length === 0;

  return {
    isSupported,
    missingFeatures,
    error: !isSupported ? SCREEN_RECORDING_ERRORS.BROWSER_COMPATIBILITY : undefined
  };
}

export function getSupportedMimeTypes(): string[] {
  const types = [
    'video/webm;codecs=vp9',
    'video/webm;codecs=vp8',
    'video/webm',
    'video/mp4;codecs=h264',
    'video/mp4'
  ];

  return types.filter(type => {
    try {
      return MediaRecorder.isTypeSupported(type);
    } catch {
      return false;
    }
  });
}

export function getBestMimeType(): string {
  const supportedTypes = getSupportedMimeTypes();
  
  // Prefer VP9 for better compression
  if (supportedTypes.includes('video/webm;codecs=vp9')) {
    return 'video/webm;codecs=vp9';
  }
  
  // Fallback to VP8
  if (supportedTypes.includes('video/webm;codecs=vp8')) {
    return 'video/webm;codecs=vp8';
  }
  
  // Fallback to basic WebM
  if (supportedTypes.includes('video/webm')) {
    return 'video/webm';
  }
  
  // Fallback to MP4 if available
  if (supportedTypes.includes('video/mp4')) {
    return 'video/mp4';
  }
  
  // Return empty string if nothing is supported
  return '';
}

export function formatErrorForUser(error: ScreenRecordingError): string {
  let message = error.userMessage;
  if (error.suggestion) {
    message += `. ${error.suggestion}`;
  }
  return message;
}
