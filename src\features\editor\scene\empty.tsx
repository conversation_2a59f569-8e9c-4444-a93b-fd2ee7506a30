import useStore from "../store/use-store";
import { useEffect, useRef, useState } from "react";
import { Droppable } from "@/components/ui/droppable";
import { Button } from "@/components/ui/button";
import { PlusIcon, Monitor, Upload } from "lucide-react";
import { DroppableArea } from "./droppable";
import { useScreenRecording } from "../hooks/use-screen-recording";
import { useScreenRecordingActions } from "../store/use-screen-recording-store";
import { useLocalVideosStore } from "../store/use-local-videos-store";
import { dispatch } from "@designcombo/events";
import { ADD_VIDEO } from "@designcombo/state";
import { generateId } from "@designcombo/timeline";
import { IVideo } from "@designcombo/types";

const SceneEmpty = () => {
  const [isLoading, setIsLoading] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const [desiredSize, setDesiredSize] = useState({ width: 0, height: 0 });
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const { size } = useStore();

  // File input ref for video uploads
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Screen recording functionality
  const { addRecording, addToTimeline } = useScreenRecordingActions();
  const { actions: videoActions } = useLocalVideosStore();

  const {
    isRecording,
    isSupported: isScreenRecordingSupported,
    startRecording,
    cleanup,
  } = useScreenRecording({
    onRecordingComplete: async (blob, duration) => {
      try {
        const recording = await addRecording(blob, duration);
        await addToTimeline(recording);
      } catch (error) {
        console.error("Failed to process screen recording:", error);
      }
    },
  });

  useEffect(() => {
    // Use the actual canvas size since scaling is handled by the parent Scene component
    setDesiredSize({
      width: size.width,
      height: size.height,
    });
    setIsLoading(false);
  }, [size]);

  // Cleanup screen recording on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const onSelectFiles = async (files: File[]) => {
    for (const file of files) {
      if (file.type.startsWith("video/")) {
        try {
          await videoActions.addVideo(file);
        } catch (error) {
          console.error("Failed to add video:", error);
        }
      }
    }
  };

  const handleFileUpload = async (files: File[]) => {
    // Clear previous errors
    setUploadErrors([]);

    let hasSuccessfulUploads = false;

    for (const file of files) {
      if (file.type.startsWith("video/")) {
        try {
          const localVideo = await videoActions.addVideo(file);

          // Automatically add to timeline
          const videoData: Partial<IVideo> = {
            id: generateId(),
            details: {
              src: localVideo.objectUrl,
              width: localVideo.width,
              height: localVideo.height,
              blur: 0,
              brightness: 100,
              flipX: false,
              flipY: false,
              rotate: "0",
              visibility: "visible",
            },
            type: "video",
            metadata: {
              previewUrl: localVideo.thumbnailUrl || localVideo.objectUrl,
              localVideoId: localVideo.id,
              fileName: localVideo.name,
            },
            duration: localVideo.duration,
          };

          dispatch(ADD_VIDEO, {
            payload: videoData,
            options: {
              resourceId: "main",
              scaleMode: "fit",
            },
          });

          hasSuccessfulUploads = true;
        } catch (error) {
          console.error("Failed to add video:", error);
          const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
          const fullErrorMessage = `Failed to upload "${file.name}": ${errorMessage}`;
          setUploadErrors(prev => [...prev, fullErrorMessage]);
        }
      }
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const videoFiles = Array.from(files).filter(file => file.type.startsWith("video/"));
      if (videoFiles.length > 0) {
        handleFileUpload(videoFiles);
      }
    }
    // Reset the input value so the same file can be selected again
    event.target.value = '';
  };

  const handleStartScreenRecording = async () => {
    try {
      await startRecording();
    } catch (error) {
      console.error("Failed to start screen recording:", error);
    }
  };

  return (
    <div ref={containerRef} className="absolute z-50 flex h-full w-full flex-1">
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="video/*,.mp4,.mov,.avi,.mkv,.webm"
        multiple
        onChange={handleFileInputChange}
        className="hidden"
      />

      {!isLoading ? (
        <Droppable
          maxFileCount={4}
          maxSize={4 * 1024 * 1024}
          disabled={false}
          onValueChange={onSelectFiles}
          className="h-full w-full flex-1 bg-background"
        >
          <DroppableArea
            onDragStateChange={setIsDraggingOver}
            className={`absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 transform items-center justify-center border border-dashed text-center transition-colors duration-200 ease-in-out ${
              isDraggingOver ? "border-white bg-white/10" : "border-white/15"
            }`}
            style={{
              width: desiredSize.width,
              height: desiredSize.height,
            }}
          >
            <div className="flex flex-col items-center justify-center gap-8 pb-12">
              {/* Error Display */}
              {uploadErrors.length > 0 && (
                <div className="w-full max-w-md space-y-2">
                  {uploadErrors.map((error, index) => (
                    <div key={index} className="flex items-start gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                      <Upload className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-destructive">{error}</p>
                    </div>
                  ))}
                </div>
              )}

              {/* Main Screen Recording Option */}
              <div className="flex flex-col items-center gap-6">
                <div className="relative">
                  <Button
                    onClick={handleStartScreenRecording}
                    disabled={!isScreenRecordingSupported || isRecording}
                    className="flex items-center gap-3 px-8 py-6 h-auto text-lg font-semibold shadow-lg"
                    variant={isRecording ? "secondary" : "default"}
                    size="lg"
                  >
                    <Monitor className="h-8 w-8" />
                    {isRecording ? "Recording..." : "Screen Record"}
                  </Button>
                  {isRecording && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
                  )}
                </div>
                <div className="flex flex-col gap-2 text-center max-w-md">
                  <h2 className="text-xl font-semibold text-foreground">
                    {isRecording ? "Recording your screen..." : "Start Screen Recording"}
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    {isScreenRecordingSupported
                      ? "Capture your screen with automatic and follow-cursor zooms. Perfect for tutorials, demos, and presentations."
                      : "Screen recording is not supported in this browser. Please use Chrome, Firefox, or Edge."
                    }
                  </p>
                </div>
              </div>

              {/* Secondary Upload Option */}
              <div className="flex flex-col items-center gap-4 pt-4 border-t border-border/50 w-full max-w-md">
                <div className="flex items-center gap-3 text-muted-foreground/70">
                  <div className="flex-1 h-px bg-border/50"></div>
                  <span className="text-xs font-medium">OR</span>
                  <div className="flex-1 h-px bg-border/50"></div>
                </div>
                <div className="flex flex-col items-center gap-3">
                  <div
                    onClick={handleUploadClick}
                    className="hover:bg-muted cursor-pointer rounded-lg border border-dashed border-border/50 p-4 text-muted-foreground transition-colors duration-200 hover:border-border"
                  >
                    <Upload className="h-6 w-6 mx-auto" aria-hidden="true" />
                  </div>
                  <div className="flex flex-col gap-1 text-center">
                    <p className="text-sm text-muted-foreground font-medium">Load a video file</p>
                    <p className="text-xs text-muted-foreground/70">
                      Upload existing videos to edit and enhance
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </DroppableArea>
        </Droppable>
      ) : (
        <div className="flex flex-1 items-center justify-center bg-background-subtle text-sm text-muted-foreground">
          Loading...
        </div>
      )}
    </div>
  );
};

export default SceneEmpty;
