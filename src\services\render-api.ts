// API service for communicating with the Remotion render server

interface IZoomTiming {
  startTime: number; // in milliseconds
  endTime: number;   // in milliseconds
}

interface IZoomConfig {
  maxZoomScale: number;
  bezierControlPoints: {
    p1: number;
    p2: number;
    p3: number;
    p4: number;
  };
  defaultTiming: {
    startTime: number;
    endTime: number;
  };
  zoomOut: {
    duration: number;
    enabled: boolean;
    easing: 'linear' | 'ease-out' | 'bezier';
  };
  position: {
    x: number;
    y: number;
  };
}

export interface RenderRequest {
  compositionId?: string;
  inputProps: {
    trackItemIds: string[];
    trackItemsMap: Record<string, any>;
    trackItemDetailsMap: Record<string, any>;
    transitionsMap: Record<string, any>;
    canvasSettings: any;
    duration: number;
    fps: number;
    width: number;
    height: number;
    zoomTiming?: IZoomTiming;
    zoomConfig?: IZoomConfig;
  };
  codec?: 'h264' | 'h265' | 'vp8' | 'vp9' | 'mp3' | 'aac' | 'wav';
  imageFormat?: 'jpeg' | 'png';
  quality?: number;
}

export interface RenderResponse {
  success: boolean;
  renderId: string;
  status: string;
  message?: string;
  error?: string;
}

export interface RenderStatus {
  success: boolean;
  renderId: string;
  status: 'started' | 'bundling' | 'preparing' | 'rendering' | 'completed' | 'error';
  progress: number;
  startTime: string;
  endTime?: string;
  duration?: number;
  downloadUrl?: string;
  error?: string;
}

export interface Composition {
  id: string;
  width: number;
  height: number;
  fps: number;
  durationInFrames: number;
}

export interface CompositionsResponse {
  success: boolean;
  compositions: Composition[];
  error?: string;
}

class RenderApiService {
  private baseUrl: string;
  private uploadedFiles: Map<string, string> = new Map(); // blob URL -> server URL mapping

  constructor(baseUrl: string = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await fetch(`${this.baseUrl}/health`);
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.statusText}`);
    }
    return response.json();
  }

  // Get available compositions
  async getCompositions(): Promise<CompositionsResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/compositions`);
      if (!response.ok) {
        throw new Error(`Failed to fetch compositions: ${response.statusText}`);
      }
      return response.json();
    } catch (error) {
      console.error('Error fetching compositions:', error);
      return {
        success: false,
        compositions: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Upload a file to the server (for blob URL conversion)
  async uploadFile(blob: Blob, filename: string): Promise<string> {
    const formData = new FormData();
    formData.append('file', blob, filename);

    const response = await fetch(`${this.baseUrl}/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Upload failed');
    }

    return result.fileUrl;
  }

  // Convert blob URLs to server URLs by uploading the files
  async processBlobUrls(inputProps: any): Promise<any> {
    const processedProps = JSON.parse(JSON.stringify(inputProps)); // Deep clone

    const replaceBlobUrls = async (obj: any): Promise<any> => {
      if (typeof obj === 'string' && obj.startsWith('blob:')) {
        // Check if we already uploaded this blob
        if (this.uploadedFiles.has(obj)) {
          return this.uploadedFiles.get(obj);
        }

        try {
          // Fetch the blob and upload it to the server
          const response = await fetch(obj);
          const blob = await response.blob();

          // Generate a filename based on the blob URL
          const urlParts = obj.split('/');
          const blobId = urlParts[urlParts.length - 1];
          const filename = `video-${blobId}.mp4`; // Assume it's a video file

          const serverUrl = await this.uploadFile(blob, filename);

          // Cache the mapping
          this.uploadedFiles.set(obj, serverUrl);

          console.log(`Converted blob URL to server URL: ${obj} -> ${serverUrl}`);
          return serverUrl;
        } catch (error) {
          console.error(`Failed to upload blob ${obj}:`, error);
          throw new Error(`Failed to upload media file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      if (typeof obj === 'object' && obj !== null) {
        if (Array.isArray(obj)) {
          for (let i = 0; i < obj.length; i++) {
            obj[i] = await replaceBlobUrls(obj[i]);
          }
        } else {
          for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
              obj[key] = await replaceBlobUrls(obj[key]);
            }
          }
        }
      }

      return obj;
    };

    return await replaceBlobUrls(processedProps);
  }

  // Start a render job
  async startRender(request: RenderRequest): Promise<RenderResponse> {
    try {
      // Process blob URLs before sending to server
      const processedRequest = {
        ...request,
        inputProps: await this.processBlobUrls(request.inputProps)
      };

      const response = await fetch(`${this.baseUrl}/render`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(processedRequest),
      });

      if (!response.ok) {
        throw new Error(`Render request failed: ${response.statusText}`);
      }

      return response.json();
    } catch (error) {
      console.error('Error starting render:', error);
      return {
        success: false,
        renderId: '',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Get render status
  async getRenderStatus(renderId: string): Promise<RenderStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/render/${renderId}/status`);
      if (!response.ok) {
        // Handle 404 specifically - render job not found (likely server restart)
        if (response.status === 404) {
          return {
            success: false,
            renderId,
            status: 'error',
            progress: 0,
            startTime: new Date().toISOString(),
            error: 'Render job not found. The server may have been restarted.'
          };
        }
        throw new Error(`Failed to get render status: ${response.statusText}`);
      }
      return response.json();
    } catch (error) {
      console.error('Error getting render status:', error);
      return {
        success: false,
        renderId,
        status: 'error',
        progress: 0,
        startTime: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Download rendered video
  async downloadVideo(renderId: string, filename?: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/render/${renderId}/download`);
      if (!response.ok) {
        throw new Error(`Download failed: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename || `${renderId}.mp4`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading video:', error);
      throw error;
    }
  }

  // Get download URL for a completed render
  getDownloadUrl(renderId: string): string {
    return `${this.baseUrl}/render/${renderId}/download`;
  }

  // Poll render status until completion
  async pollRenderStatus(
    renderId: string,
    onProgress?: (status: RenderStatus) => void,
    pollInterval: number = 2000
  ): Promise<RenderStatus> {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const status = await this.getRenderStatus(renderId);
          
          if (onProgress) {
            onProgress(status);
          }

          if (status.status === 'completed') {
            resolve(status);
          } else if (status.status === 'error') {
            reject(new Error(status.error || 'Render failed'));
          } else {
            // Continue polling
            setTimeout(poll, pollInterval);
          }
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }

  // Convenience method to render and wait for completion
  async renderAndWait(
    request: RenderRequest,
    onProgress?: (status: RenderStatus) => void
  ): Promise<RenderStatus> {
    const renderResponse = await this.startRender(request);
    
    if (!renderResponse.success) {
      throw new Error(renderResponse.error || 'Failed to start render');
    }

    return this.pollRenderStatus(renderResponse.renderId, onProgress);
  }

  // Check if server is available
  async isServerAvailable(): Promise<boolean> {
    try {
      await this.healthCheck();
      return true;
    } catch {
      return false;
    }
  }

  // Get benchmark information for optimal concurrency
  async getBenchmark(): Promise<{ cpuCount: number; recommendedConcurrency: number; message: string } | null> {
    try {
      const response = await fetch(`${this.baseUrl}/benchmark`);
      if (!response.ok) {
        throw new Error(`Benchmark request failed: ${response.statusText}`);
      }
      const data = await response.json();
      return data.success ? data : null;
    } catch (error) {
      console.error('Benchmark request failed:', error);
      return null;
    }
  }
}

// Export singleton instance
export const renderApi = new RenderApiService();

// Export class for custom instances
export { RenderApiService };

// Helper function to create render request from editor state
export function createRenderRequest(
  trackItemIds: string[],
  trackItemsMap: Record<string, any>,
  trackItemDetailsMap: Record<string, any>,
  transitionsMap: Record<string, any>,
  canvasSettings: any,
  duration: number,
  fps: number = 30,
  width: number = 1080,
  height: number = 1920,
  options: {
    compositionId?: string;
    codec?: RenderRequest['codec'];
    imageFormat?: RenderRequest['imageFormat'];
    quality?: number;
    zoomTiming?: IZoomTiming;
    zoomConfig?: IZoomConfig;
  } = {}
): RenderRequest {
  return {
    compositionId: options.compositionId || 'Composition',
    inputProps: {
      trackItemIds,
      trackItemsMap,
      trackItemDetailsMap,
      transitionsMap,
      canvasSettings,
      duration,
      fps,
      width,
      height,
      zoomTiming: options.zoomTiming,
      zoomConfig: options.zoomConfig,
    },
    codec: options.codec || 'h264',
    imageFormat: options.imageFormat || 'jpeg',
    quality: options.quality || 80,
  };
}
