import { useState, useRef, useCallback } from 'react';
import {
  getScreenRecordingError,
  checkBrowserCompatibility,
  getBestMimeType,
  formatErrorForUser
} from '../utils/screen-recording-errors';

export interface ScreenRecordingState {
  isRecording: boolean;
  isPaused: boolean;
  isSupported: boolean;
  recordedBlob: Blob | null;
  duration: number;
  error: string | null;
}

export interface ScreenRecordingControls {
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  pauseRecording: () => void;
  resumeRecording: () => void;
  clearRecording: () => void;
}

export interface UseScreenRecordingOptions {
  mimeType?: string;
  videoBitsPerSecond?: number;
  audioBitsPerSecond?: number;
  onRecordingComplete?: (blob: Blob, duration: number) => void;
  onError?: (error: string) => void;
}

export function useScreenRecording(options: UseScreenRecordingOptions = {}) {
  const {
    videoBitsPerSecond = 2500000, // 2.5 Mbps
    audioBitsPerSecond = 128000,  // 128 kbps
    onRecordingComplete,
    onError,
  } = options;

  // Check browser compatibility and get best mime type
  const compatibility = checkBrowserCompatibility();
  const bestMimeType = options.mimeType || getBestMimeType();

  const [state, setState] = useState<ScreenRecordingState>({
    isRecording: false,
    isPaused: false,
    isSupported: compatibility.isSupported,
    recordedBlob: null,
    duration: 0,
    error: compatibility.error ? formatErrorForUser(compatibility.error) : null,
  });

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const startTimeRef = useRef<number>(0);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const updateDuration = useCallback(() => {
    if (startTimeRef.current > 0) {
      const elapsed = Date.now() - startTimeRef.current;
      setState(prev => ({ ...prev, duration: elapsed }));
    }
  }, []);

  const startDurationTimer = useCallback(() => {
    startTimeRef.current = Date.now();
    durationIntervalRef.current = setInterval(updateDuration, 100);
  }, [updateDuration]);

  const stopDurationTimer = useCallback(() => {
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
  }, []);

  const clearRecording = useCallback(() => {
    setState(prev => ({
      ...prev,
      recordedBlob: null,
      duration: 0,
      error: null,
    }));
    chunksRef.current = [];
  }, []);

  const handleError = useCallback((error: unknown) => {
    const screenRecordingError = getScreenRecordingError(error);
    const userFriendlyMessage = formatErrorForUser(screenRecordingError);

    setState(prev => ({ ...prev, error: userFriendlyMessage, isRecording: false, isPaused: false }));
    stopDurationTimer();
    onError?.(userFriendlyMessage);
  }, [onError, stopDurationTimer]);

  const startRecording = useCallback(async () => {
    if (!state.isSupported) {
      handleError(new Error('Screen recording is not supported in this browser'));
      return;
    }

    try {
      // Clear any previous recording
      clearRecording();

      // Request screen capture
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: { ideal: 30 },
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100,
        },
      });

      streamRef.current = stream;

      // Use the best available mime type
      const finalMimeType = bestMimeType;
      if (!finalMimeType) {
        throw new Error('No supported video format available');
      }

      // Create MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: finalMimeType,
        videoBitsPerSecond,
        audioBitsPerSecond,
      });

      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      // Set up event handlers
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: finalMimeType || 'video/webm' });
        const finalDuration = Date.now() - startTimeRef.current;
        
        setState(prev => ({
          ...prev,
          recordedBlob: blob,
          isRecording: false,
          isPaused: false,
          duration: finalDuration,
        }));

        stopDurationTimer();
        onRecordingComplete?.(blob, finalDuration);

        // Clean up stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
      };

      mediaRecorder.onerror = (event) => {
        handleError(`Recording error: ${event.error?.message || 'Unknown error'}`);
      };

      // Handle user stopping screen share
      stream.getVideoTracks()[0].addEventListener('ended', () => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
          mediaRecorderRef.current.stop();
        }
      });

      // Start recording
      mediaRecorder.start(1000); // Collect data every second
      startDurationTimer();

      setState(prev => ({
        ...prev,
        isRecording: true,
        isPaused: false,
        error: null,
      }));

    } catch (error) {
      handleError(error);
    }
  }, [state.isSupported, bestMimeType, videoBitsPerSecond, audioBitsPerSecond, clearRecording, handleError, startDurationTimer, stopDurationTimer, onRecordingComplete]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }
  }, []);

  const pauseRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.pause();
      stopDurationTimer();
      setState(prev => ({ ...prev, isPaused: true }));
    }
  }, [stopDurationTimer]);

  const resumeRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'paused') {
      mediaRecorderRef.current.resume();
      startDurationTimer();
      setState(prev => ({ ...prev, isPaused: false }));
    }
  }, [startDurationTimer]);

  // Cleanup on unmount
  const cleanup = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    stopDurationTimer();
  }, [stopDurationTimer]);

  return {
    ...state,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    clearRecording,
    cleanup,
  };
}
