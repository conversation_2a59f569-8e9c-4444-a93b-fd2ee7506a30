import React from 'react';
import { AbsoluteFill, useVideoConfig, getInputProps, useCurrentFrame } from 'remotion';
import { CanvasSettings } from '../features/editor/store/use-canvas-store';
import { ITrackItem, ITransition } from '@designcombo/types';
import { groupTrackItems } from '../features/editor/utils/track-items';
import { SequenceItem } from '../features/editor/player/sequence-item';
import { merge } from 'lodash';
import { useZoomStore, IZoomConfig } from '../features/editor/store/use-zoom-store';
import { calculateZoomScaleFromFrame } from '../features/editor/utils/zoom-calculations';

interface IZoomTiming {
  startTime: number; // in milliseconds
  endTime: number;   // in milliseconds
}

interface IZoomEffect {
  id: string;
  startTime: number;
  endTime: number;
  maxZoomScale?: number;
  zoomArea?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

interface VideoEditorCompositionProps {
  canvasSettings: CanvasSettings;
  width: number;
  height: number;
  fps: number;
  durationInFrames: number;
  trackItemIds?: string[];
  trackItemsMap?: Record<string, ITrackItem>;
  trackItemDetailsMap?: Record<string, any>;
  transitionsMap?: Record<string, ITransition>;
  zoomTiming?: IZoomTiming; // Legacy support
  zoomEffects?: IZoomEffect[]; // New multiple zoom effects
  zoomConfig?: IZoomConfig;
}

export const VideoEditorComposition: React.FC<VideoEditorCompositionProps> = (props) => {
  const { width, height } = useVideoConfig();
  const { config: zoomConfig } = useZoomStore();

  // Try to get input props for export context, fallback to direct props for player context
  let inputProps: VideoEditorCompositionProps;
  try {
    const remotionProps = getInputProps();
    inputProps = {
      canvasSettings: props.canvasSettings,
      width: props.width,
      height: props.height,
      fps: props.fps,
      durationInFrames: props.durationInFrames,
      ...remotionProps
    } as VideoEditorCompositionProps;
  } catch {
    // If getInputProps fails (e.g., in Player context), use direct props
    inputProps = props;
  }

  const {
    canvasSettings = {
      background: {
        type: 'solid',
        solidColor: '#000000',
        gradient: { type: 'linear', angle: 0, stops: [] },
        imageUrl: null,
        imageFile: null,
        imageObjectUrl: null,
      },
      padding: { value: 70, unit: 'px' },
      blur: { enabled: false, intensity: 0 },
      videoBorderRadius: { value: 0 },
      videoBackgroundShadow: {
        enabled: false,
        x: 0,
        y: 0,
        blur: 0,
        spread: 0,
        color: 'rgba(0, 0, 0, 0)',
      },
    },
    trackItemIds = [],
    trackItemsMap = {},
    trackItemDetailsMap = {},
    transitionsMap = {},
    fps: propsFps = 30,
    zoomTiming = { startTime: 0, endTime: 0 }, // Legacy support - no default zoom timing
    zoomEffects = [], // New multiple zoom effects
    zoomConfig: inputZoomConfig // Zoom config from render request
  } = inputProps;

  // Use zoom config from input props (during rendering) or fall back to store (during preview)
  const effectiveZoomConfig = inputZoomConfig || zoomConfig;

  // Calculate canvas dimensions with padding (matching canvas-container.tsx logic)
  const paddingValue = canvasSettings.padding.unit === "%"
    ? Math.min(width, height) * (Math.max(0, Math.min(50, canvasSettings.padding.value)) / 100)
    : Math.max(0, Math.min(200, canvasSettings.padding.value));

  const canvasWidth = width;
  const canvasHeight = height;

  // Calculate available space after padding
  const availableWidth = canvasWidth - (paddingValue * 2);
  const availableHeight = canvasHeight - (paddingValue * 2);

  // Calculate scale to fit video within available space while maintaining aspect ratio
  const originalAspectRatio = width / height;
  const availableAspectRatio = availableWidth / availableHeight;

  let videoWidth, videoHeight;

  if (originalAspectRatio > availableAspectRatio) {
    // Video is wider relative to available space - fit to width
    videoWidth = availableWidth;
    videoHeight = availableWidth / originalAspectRatio;
  } else {
    // Video is taller relative to available space - fit to height
    videoHeight = availableHeight;
    videoWidth = availableHeight * originalAspectRatio;
  }

  // Center the video within the canvas
  const videoLeft = (canvasWidth - videoWidth) / 2;
  const videoTop = (canvasHeight - videoHeight) / 2;

  // Calculate scale factor for the video content
  const scale = videoWidth / width;

  // Debug logging for export composition
  console.log('🎬 REMOTION EXPORT DEBUG:');
  console.log(`📐 Input dimensions: ${width}x${height}`);
  console.log(`📏 Padding value: ${paddingValue}px (${canvasSettings.padding.value}${canvasSettings.padding.unit})`);
  console.log(`📦 Canvas dimensions: ${canvasWidth}x${canvasHeight}`);
  console.log(`🎥 Video dimensions: ${videoWidth}x${videoHeight}`);
  console.log(`📍 Video position: left=${videoLeft}, top=${videoTop}`);
  console.log(`🔍 Scale factor: ${scale}`);
  console.log(`🎨 Background type: ${canvasSettings.background.type}`);
  console.log(`🖼️ Background image URL: ${canvasSettings.background.imageObjectUrl ? 'Present' : 'None'}`);
  console.log(`💫 Blur enabled: ${canvasSettings.blur.enabled}, intensity: ${canvasSettings.blur.intensity}`);
  console.log(`🔄 Available space: ${availableWidth}x${availableHeight}`);
  console.log(`📊 Original aspect ratio: ${originalAspectRatio}, Available aspect ratio: ${availableAspectRatio}`);

  // Generate background style
  const getBackgroundStyle = (): React.CSSProperties => {
    let backgroundImage = "";
    let backgroundColor = "";
    let filter = "";

    switch (canvasSettings.background.type) {
      case "solid":
        backgroundColor = canvasSettings.background.solidColor;
        break;

      case "gradient":
        const { gradient } = canvasSettings.background;
        const stops = gradient.stops
          .map(stop => `${stop.color} ${stop.position}%`)
          .join(", ");

        if (gradient.type === "linear") {
          backgroundImage = `linear-gradient(${gradient.angle}deg, ${stops})`;
        } else {
          backgroundImage = `radial-gradient(circle, ${stops})`;
        }
        break;

      case "image":
        if (canvasSettings.background.imageObjectUrl) {
          backgroundImage = `url(${canvasSettings.background.imageObjectUrl})`;
        }
        break;
    }

    // Apply blur to all background types if enabled
    if (canvasSettings.blur.enabled && canvasSettings.blur.intensity > 0) {
      filter = `blur(${canvasSettings.blur.intensity}px)`;
    }

    const style: React.CSSProperties = {
      filter,
    };

    // Set background properties based on type (matching canvas-container.tsx)
    if (canvasSettings.background.type === "image") {
      style.backgroundImage = backgroundImage;
      style.backgroundSize = "100% auto";
      style.backgroundPosition = "center";
      style.backgroundRepeat = "no-repeat";
    } else if (canvasSettings.background.type === "gradient") {
      style.backgroundImage = backgroundImage;
    } else {
      style.backgroundColor = backgroundColor;
    }

    return style;
  };

  // Generate video background style (shadow)
  const getVideoBackgroundStyle = (): React.CSSProperties => {
    const { videoBackgroundShadow, videoBorderRadius } = canvasSettings;

    let boxShadow = "";

    // Apply shadow if enabled
    if (videoBackgroundShadow.enabled) {
      boxShadow = `${videoBackgroundShadow.x}px ${videoBackgroundShadow.y}px ${videoBackgroundShadow.blur}px ${videoBackgroundShadow.spread}px ${videoBackgroundShadow.color}`;
    }

    return {
      boxShadow,
      borderRadius: `${videoBorderRadius.value}px`,
    };
  };

  // Process timeline data to render actual content
  const mergedTrackItemsDetailsMap = merge(trackItemsMap, trackItemDetailsMap);
  const groupedItems = groupTrackItems({
    trackItemIds,
    transitionsMap,
    trackItemsMap: mergedTrackItemsDetailsMap,
  });

  // Zoom effect logic for the entire canvas using centralized zoom utility
  const getCanvasZoomScale = () => {
    const frame = useCurrentFrame();
    const currentTimeMs = (frame / propsFps) * 1000;

    // Check for active zoom effects (prioritize new multiple zoom effects over legacy single zoom)
    const activeZoomEffects = zoomEffects.length > 0 ? zoomEffects : [];

    // If we have multiple zoom effects, find the one that's active at current time
    if (activeZoomEffects.length > 0) {
      const activeEffectsAtTime = activeZoomEffects.filter(effect =>
        currentTimeMs >= effect.startTime && currentTimeMs <= effect.endTime
      );

      // If multiple effects are active at the same time, use the most recently created one
      // (effects are added to the end of the array, so the last one is most recent)
      const activeEffect = activeEffectsAtTime.length > 0
        ? activeEffectsAtTime[activeEffectsAtTime.length - 1]
        : null;

      if (activeEffect) {
        // Create zoom timing for this specific effect
        const effectZoomTiming = {
          startTime: activeEffect.startTime,
          endTime: activeEffect.endTime
        };

        // Create a modified zoom config that uses the effect's specific settings
        const dynamicZoomConfig = {
          ...effectiveZoomConfig,
          maxZoomScale: activeEffect.maxZoomScale || effectiveZoomConfig.calculatedZoomScale || effectiveZoomConfig.maxZoomScale,
          zoomArea: activeEffect.zoomArea || effectiveZoomConfig.zoomArea
        };

        // Use centralized zoom calculation with dynamic zoom config
        const zoomResult = calculateZoomScaleFromFrame(
          frame,
          propsFps,
          effectZoomTiming,
          dynamicZoomConfig
        );

        return zoomResult.zoomScale;
      }
    }

    // Fallback to legacy single zoom timing if no multiple zoom effects are active
    if (zoomTiming.startTime !== 0 || zoomTiming.endTime !== 0) {
      // Create a modified zoom config that uses the calculated zoom scale
      const dynamicZoomConfig = {
        ...effectiveZoomConfig,
        maxZoomScale: effectiveZoomConfig.calculatedZoomScale || effectiveZoomConfig.maxZoomScale
      };

      // Use centralized zoom calculation with dynamic zoom config
      const zoomResult = calculateZoomScaleFromFrame(
        frame,
        propsFps,
        zoomTiming,
        dynamicZoomConfig
      );

      return zoomResult.zoomScale;
    }

    // No zoom effects active
    return 1.0;
  };

  // Render timeline items
  const renderTimelineContent = () => {
    if (trackItemIds.length === 0) {
      // Show placeholder when no timeline items
      return (
        <div style={{ textAlign: "center" }}>
          <div>Video Content</div>
          <div style={{ fontSize: "16px", marginTop: "10px", opacity: 0.7 }}>
            {width} × {height}
          </div>
        </div>
      );
    }

    // Render actual timeline items
    return groupedItems.map((group, groupIndex) => {
      return group.map((groupItem) => {
        const item = mergedTrackItemsDetailsMap[groupItem.id];
        if (item && SequenceItem[item.type]) {
          return SequenceItem[item.type](item, {
            fps: propsFps,
            // Don't pass zoomTiming to individual items anymore
          });
        }
        return null;
      });
    }).flat().filter(Boolean);
  };

  const canvasZoomScale = getCanvasZoomScale();

  // Get the transform origin for the current zoom effect
  const getTransformOrigin = () => {
    const frame = useCurrentFrame();
    const currentTimeMs = (frame / propsFps) * 1000;

    // Check for active zoom effects
    if (zoomEffects.length > 0) {
      const activeEffect = zoomEffects.find(effect =>
        currentTimeMs >= effect.startTime && currentTimeMs <= effect.endTime
      );

      if (activeEffect && activeEffect.zoomArea) {
        return `${(activeEffect.zoomArea.x + activeEffect.zoomArea.width / 2) * 100}% ${(activeEffect.zoomArea.y + activeEffect.zoomArea.height / 2) * 100}%`;
      }
    }

    // Fallback to default zoom area or position
    return effectiveZoomConfig.zoomArea
      ? `${(effectiveZoomConfig.zoomArea.x + effectiveZoomConfig.zoomArea.width / 2) * 100}% ${(effectiveZoomConfig.zoomArea.y + effectiveZoomConfig.zoomArea.height / 2) * 100}%`
      : `${(effectiveZoomConfig.position?.x || 0.5) * 100}% ${(effectiveZoomConfig.position?.y || 0.5) * 100}%`;
  };

  return (
    <AbsoluteFill>
      {/* Canvas Container with Zoom Effect */}
      <div
        style={{
          width: canvasWidth,
          height: canvasHeight,
          position: "relative",
          transform: `scale(${canvasZoomScale})`,
          transformOrigin: getTransformOrigin(),
        }}
      >
        {/* Background layer - can be blurred independently */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            ...getBackgroundStyle(),
          }}
        />

        {/* Video container positioned within the canvas */}
        <div
          style={{
            position: "absolute",
            left: videoLeft,
            top: videoTop,
            width: videoWidth,
            height: videoHeight,
            overflow: "hidden",
            ...getVideoBackgroundStyle(),
          }}
        >
          <div
            style={{
              width: width,
              height: height,
              transform: `scale(${scale})`,
              transformOrigin: "top left",
              borderRadius: `${canvasSettings.videoBorderRadius.value}px`,
              overflow: "hidden",
              position: "relative",
            }}
          >
            {/* Render timeline content or placeholder */}
            {trackItemIds.length > 0 ? (
              <AbsoluteFill>
                {renderTimelineContent()}
              </AbsoluteFill>
            ) : (
              <div
                style={{
                  width: "100%",
                  height: "100%",
                  backgroundColor: "#1a1a1a",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "white",
                  fontSize: "24px",
                  fontFamily: "Arial, sans-serif",
                }}
              >
                <div style={{ textAlign: "center" }}>
                  <div>Video Content</div>
                  <div style={{ fontSize: "16px", marginTop: "10px", opacity: 0.7 }}>
                    {width} × {height}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};
