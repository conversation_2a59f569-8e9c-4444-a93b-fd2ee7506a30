import React, { useCallback, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useScreenRecording } from "../hooks/use-screen-recording";
import { useScreenRecordingActions, useScreenRecordingProcessing } from "../store/use-screen-recording-store";
import { 
  Monitor, 
  Square, 
  Play, 
  Pause, 
  Trash2, 
  Download, 
  Plus,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";
import { cn } from "@/lib/utils";

interface ScreenRecordingProps {
  onRecordingComplete?: () => void;
  className?: string;
}

export function ScreenRecording({ onRecordingComplete, className }: ScreenRecordingProps) {
  const { addRecording, addToTimeline } = useScreenRecordingActions();
  const isProcessing = useScreenRecordingProcessing();

  const {
    isRecording,
    isPaused,
    isSupported,
    recordedBlob,
    duration,
    error,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    clearRecording,
    cleanup,
  } = useScreenRecording({
    onRecordingComplete: async (blob, recordingDuration) => {
      try {
        const recording = await addRecording(blob, recordingDuration);
        // Automatically add to timeline
        await addToTimeline(recording);
        onRecordingComplete?.();
      } catch (error) {
        console.error("Failed to process recording:", error);
      }
    },
    onError: (errorMessage) => {
      console.error("Screen recording error:", errorMessage);
    },
  });

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const formatDuration = useCallback((ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  const handleStartRecording = useCallback(async () => {
    try {
      await startRecording();
    } catch (error) {
      console.error("Failed to start recording:", error);
    }
  }, [startRecording]);

  const handleDownload = useCallback(() => {
    if (recordedBlob) {
      const url = URL.createObjectURL(recordedBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `screen-recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  }, [recordedBlob]);

  if (!isSupported) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            Screen Recording Not Supported
          </CardTitle>
          <CardDescription>
            Your browser doesn't support screen recording. Please use a modern browser like Chrome, Firefox, or Edge.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full border-2", className)}>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-3 text-xl">
          <Monitor className="h-6 w-6" />
          Screen Recording Studio
        </CardTitle>
        <CardDescription className="text-base">
          Capture your screen with professional quality. Perfect for tutorials, demos, and presentations.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Recording Status */}
        {(isRecording || isPaused) && (
          <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                isRecording && !isPaused ? "bg-red-500 animate-pulse" : "bg-yellow-500"
              )} />
              <span className="text-sm font-medium">
                {isPaused ? "Recording Paused" : "Recording..."}
              </span>
              <Badge variant="secondary" className="text-xs">
                {formatDuration(duration)}
              </Badge>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <AlertCircle className="h-4 w-4 text-destructive" />
            <span className="text-sm text-destructive">{error}</span>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            <span className="text-sm text-blue-700">Processing recording...</span>
          </div>
        )}

        {/* Recording Complete */}
        {recordedBlob && !isRecording && !isPaused && (
          <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-700">
              Recording complete! ({formatDuration(duration)})
            </span>
          </div>
        )}

        {/* Controls */}
        <div className="flex flex-wrap gap-3 justify-center">
          {!isRecording && !isPaused && !recordedBlob && (
            <Button
              onClick={handleStartRecording}
              className="flex items-center gap-3 px-8 py-6 h-auto text-lg font-semibold shadow-lg"
              disabled={isProcessing}
              size="lg"
            >
              <Monitor className="h-6 w-6" />
              Start Screen Recording
            </Button>
          )}

          {isRecording && !isPaused && (
            <>
              <Button
                onClick={pauseRecording}
                variant="outline"
                className="flex items-center gap-2 px-6 py-3"
                size="lg"
              >
                <Pause className="h-5 w-5" />
                Pause Recording
              </Button>
              <Button
                onClick={stopRecording}
                variant="destructive"
                className="flex items-center gap-2 px-6 py-3"
                size="lg"
              >
                <Square className="h-5 w-5" />
                Stop Recording
              </Button>
            </>
          )}

          {isPaused && (
            <>
              <Button
                onClick={resumeRecording}
                className="flex items-center gap-2 px-6 py-3"
                size="lg"
              >
                <Play className="h-5 w-5" />
                Resume Recording
              </Button>
              <Button
                onClick={stopRecording}
                variant="destructive"
                className="flex items-center gap-2 px-6 py-3"
                size="lg"
              >
                <Square className="h-5 w-5" />
                Stop Recording
              </Button>
            </>
          )}

          {recordedBlob && !isRecording && !isPaused && (
            <>
              <Button
                onClick={handleStartRecording}
                className="flex items-center gap-3 px-8 py-6 h-auto text-lg font-semibold shadow-lg"
                disabled={isProcessing}
                size="lg"
              >
                <Plus className="h-6 w-6" />
                Record Another Video
              </Button>
              <Button
                onClick={handleDownload}
                variant="outline"
                className="flex items-center gap-2 px-4 py-3"
              >
                <Download className="h-4 w-4" />
                Download
              </Button>
              <Button
                onClick={clearRecording}
                variant="outline"
                className="flex items-center gap-2 px-4 py-3"
              >
                <Trash2 className="h-4 w-4" />
                Clear
              </Button>
            </>
          )}
        </div>

        {/* Recording Tips */}
        {!isRecording && !isPaused && !recordedBlob && (
          <div className="bg-muted/30 rounded-lg p-4 space-y-2">
            <h4 className="text-sm font-medium text-foreground mb-2">📹 How it works:</h4>
            <div className="text-sm text-muted-foreground space-y-1">
              <p>• Click "Start Screen Recording" to begin</p>
              <p>• Select which screen, window, or tab to record</p>
              <p>• Your recording will automatically appear in the timeline</p>
              <p>• Perfect for tutorials, demos, and presentations</p>
            </div>
          </div>
        )}

        {/* Success Message */}
        {recordedBlob && !isRecording && !isPaused && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <h4 className="text-sm font-medium text-green-800 mb-1">🎉 Recording Complete!</h4>
            <p className="text-sm text-green-700">
              Your screen recording has been added to the timeline and is ready for editing.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default ScreenRecording;
