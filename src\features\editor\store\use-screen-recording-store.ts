import { create } from "zustand";
import { generateId } from "@designcombo/timeline";
import { getVideoMetadata } from "../utils/file";
import { useLocalVideosStore } from "./use-local-videos-store";
import { dispatch } from "@designcombo/events";
import { ADD_VIDEO } from "@designcombo/state";
import { IVideo } from "@designcombo/types";

export interface ScreenRecording {
  id: string;
  blob: Blob;
  name: string;
  duration: number;
  width: number;
  height: number;
  aspectRatio: number;
  objectUrl: string;
  thumbnailUrl?: string;
  type: "screen-recording";
  recordedAt: Date;
  mimeType: string;
}

export interface ScreenRecordingState {
  recordings: ScreenRecording[];
  isProcessing: boolean;
  currentRecording: ScreenRecording | null;
  actions: {
    addRecording: (blob: Blob, duration: number, mimeType?: string) => Promise<ScreenRecording>;
    removeRecording: (id: string) => void;
    clearAll: () => void;
    getRecordingById: (id: string) => ScreenRecording | undefined;
    addToTimeline: (recording: ScreenRecording) => Promise<void>;
    setCurrentRecording: (recording: ScreenRecording | null) => void;
  };
}

// Helper function to create video thumbnail from blob
const createThumbnailFromBlob = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      reject(new Error("Could not get canvas context"));
      return;
    }

    const objectUrl = URL.createObjectURL(blob);
    
    const timeout = setTimeout(() => {
      reject(new Error("Thumbnail generation timed out"));
      URL.revokeObjectURL(objectUrl);
    }, 10000);

    video.onloadedmetadata = () => {
      canvas.width = 160;
      canvas.height = 90;
      video.currentTime = Math.min(1, video.duration * 0.1);
    };

    video.onseeked = () => {
      try {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const thumbnailUrl = canvas.toDataURL("image/jpeg", 0.8);
        clearTimeout(timeout);
        URL.revokeObjectURL(objectUrl);
        resolve(thumbnailUrl);
      } catch (error) {
        clearTimeout(timeout);
        URL.revokeObjectURL(objectUrl);
        reject(error);
      }
    };

    video.onerror = () => {
      clearTimeout(timeout);
      URL.revokeObjectURL(objectUrl);
      reject(new Error("Failed to load video for thumbnail"));
    };

    video.src = objectUrl;
    video.load();
  });
};

// Helper function to get video dimensions from blob
const getVideoDimensions = (blob: Blob): Promise<{ width: number; height: number; duration: number }> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    const objectUrl = URL.createObjectURL(blob);

    const timeout = setTimeout(() => {
      reject(new Error("Video metadata loading timed out"));
      URL.revokeObjectURL(objectUrl);
    }, 10000);

    video.onloadedmetadata = () => {
      const dimensions = {
        width: video.videoWidth,
        height: video.videoHeight,
        duration: video.duration * 1000, // Convert to milliseconds
      };
      clearTimeout(timeout);
      URL.revokeObjectURL(objectUrl);
      resolve(dimensions);
    };

    video.onerror = () => {
      clearTimeout(timeout);
      URL.revokeObjectURL(objectUrl);
      reject(new Error("Failed to load video metadata"));
    };

    video.src = objectUrl;
    video.load();
  });
};

export const useScreenRecordingStore = create<ScreenRecordingState>((set, get) => ({
  recordings: [],
  isProcessing: false,
  currentRecording: null,

  actions: {
    addRecording: async (blob: Blob, duration: number, mimeType = "video/webm") => {
      set({ isProcessing: true });

      try {
        const id = generateId();
        const objectUrl = URL.createObjectURL(blob);
        const recordedAt = new Date();
        
        // Generate a name based on timestamp
        const timestamp = recordedAt.toLocaleString().replace(/[/:]/g, '-');
        const name = `Screen Recording ${timestamp}`;

        // Get video dimensions
        const { width, height, duration: actualDuration } = await getVideoDimensions(blob);
        const aspectRatio = width / height;

        // Debug logging for screen recording
        console.log('📹 SCREEN RECORDING DEBUG:');
        console.log(`📐 Recorded dimensions: ${width}x${height}`);
        console.log(`📊 Aspect ratio: ${aspectRatio}`);
        console.log(`⏱️ Duration: ${actualDuration}ms`);
        console.log(`📁 Blob size: ${(blob.size / 1024 / 1024).toFixed(2)}MB`);
        console.log(`🎭 MIME type: ${mimeType}`);

        // Create thumbnail
        let thumbnailUrl: string | undefined;
        try {
          thumbnailUrl = await createThumbnailFromBlob(blob);
        } catch (error) {
          console.warn("Failed to create thumbnail:", error);
          // Continue without thumbnail
        }

        const recording: ScreenRecording = {
          id,
          blob,
          name,
          duration: actualDuration || duration,
          width,
          height,
          aspectRatio,
          objectUrl,
          thumbnailUrl,
          type: "screen-recording",
          recordedAt,
          mimeType,
        };

        set(state => ({
          recordings: [...state.recordings, recording],
          currentRecording: recording,
          isProcessing: false,
        }));

        return recording;
      } catch (error) {
        set({ isProcessing: false });
        console.error("Failed to process screen recording:", error);
        throw error;
      }
    },

    removeRecording: (id: string) => {
      const { recordings } = get();
      const recording = recordings.find(r => r.id === id);
      
      if (recording) {
        // Clean up object URL
        URL.revokeObjectURL(recording.objectUrl);
        
        set(state => ({
          recordings: state.recordings.filter(r => r.id !== id),
          currentRecording: state.currentRecording?.id === id ? null : state.currentRecording,
        }));
      }
    },

    clearAll: () => {
      const { recordings } = get();
      
      // Clean up all object URLs
      recordings.forEach(recording => {
        URL.revokeObjectURL(recording.objectUrl);
      });

      set({
        recordings: [],
        currentRecording: null,
      });
    },

    getRecordingById: (id: string) => {
      const { recordings } = get();
      return recordings.find(r => r.id === id);
    },

    addToTimeline: async (recording: ScreenRecording) => {
      try {
        // Convert screen recording to a File object for compatibility with local videos store
        const file = new File([recording.blob], recording.name, {
          type: recording.mimeType,
          lastModified: recording.recordedAt.getTime(),
        });

        // Add to local videos store first
        const localVideosStore = useLocalVideosStore.getState();
        const localVideo = await localVideosStore.actions.addVideo(file);

        // Automatically add to timeline
        const videoData: Partial<IVideo> = {
          id: generateId(),
          details: {
            src: localVideo.objectUrl,
            width: localVideo.width,
            height: localVideo.height,
            blur: 0,
            brightness: 100,
            flipX: false,
            flipY: false,
            rotate: "0",
            visibility: "visible",
          },
          type: "video",
          metadata: {
            previewUrl: localVideo.thumbnailUrl || localVideo.objectUrl,
            localVideoId: localVideo.id,
            fileName: localVideo.name,
          },
          duration: localVideo.duration,
        };

        dispatch(ADD_VIDEO, {
          payload: videoData,
          options: {
            resourceId: "main",
            scaleMode: "fit",
          },
        });

        console.log("Screen recording added to timeline successfully");
      } catch (error) {
        console.error("Failed to add screen recording to timeline:", error);
        throw error;
      }
    },

    setCurrentRecording: (recording: ScreenRecording | null) => {
      set({ currentRecording: recording });
    },
  },
}));

// Helper hook to get just the actions
export const useScreenRecordingActions = () => useScreenRecordingStore(state => state.actions);

// Helper hook to get recordings
export const useScreenRecordings = () => useScreenRecordingStore(state => state.recordings);

// Helper hook to get current recording
export const useCurrentScreenRecording = () => useScreenRecordingStore(state => state.currentRecording);

// Helper hook to get processing state
export const useScreenRecordingProcessing = () => useScreenRecordingStore(state => state.isProcessing);
